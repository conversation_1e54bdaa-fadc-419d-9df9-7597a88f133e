/*
 * @Description:
 * @Author: xiuji
 * @Date: 2024-03-25 10:12:06
 * @LastEditTime: 2025-01-26 11:12:52
 * @LastEditors: Do not edit
 */
import {
    createRouter,
    createWebHashHistory
} from 'vue-router'
import {
    useHomeStore
} from '@/store';
import {
    nextTick,
    onBeforeUnmount
} from 'vue';
import {
    useRouter,
    useRoute
} from "vue-router";
import LAYOUT from './modules/layout';
import ABILITY_ROUTER from "./modules/ability"
import LOGIN from "./modules/login"
import MAKEUP from "./modules/makeup"
import OPERATE from "./modules/operate"
import COMBINE from "./modules/combine"
import S<PERSON>UT<PERSON> from "./modules/solutionNew"
import SCENARIO from "./modules/scenario"
import PRODUCT from './modules/product'
import IFLOGIN from './modules/ifLogin'
import INFO from './modules/info'
import MODULELIST from './modules/moduleList'
import AI from './modules/AI'
import BUYLIST from './modules/buyList'
import NEWPROJECT from './modules/newProject'
//import OFFICECENTER from "./modules/officeCenter"
import DETAIL from './modules/detail'
import PREFECTURE from './modules/prefecture'
import CASESTORE from './modules/caseStore'
import AIBUYDETAIL from './modules/aiDetail'
import VISIONPOLICY from './modules/visionPolicy'
import CITYAREA from './modules/cityArea'
import COMMUNITY from './modules/community'
import DISPATCHCENTER from './modules/dispatchCenter'
import CASEPRODUCT from './modules/caseProduct'
import SCENESOLUTION from './modules/sceneSolution'
import { operate } from "@/api/login/login.js";

import {
    currentTab
} from "@/store";
import {
    use
} from 'echarts';

const Router = useRouter();
const routes = [...NEWPROJECT, ...COMMUNITY, ...VISIONPOLICY, ...CASESTORE, ...LAYOUT, ...INFO, ...ABILITY_ROUTER, ...PREFECTURE, ...MAKEUP, ...OPERATE, ...LOGIN, ...IFLOGIN, ...SOLUTIO, ...MODULELIST, ...COMBINE, ...AI, ...PRODUCT, ...SCENARIO, ...BUYLIST, ...DETAIL, ...AIBUYDETAIL, ...CITYAREA, ...DISPATCHCENTER, ...CASEPRODUCT, ...SCENESOLUTION];
const router = createRouter({
    history: createWebHashHistory(
        import.meta.env.BASE_URL),
    routes,
})

// 白名单：不需要登录即可访问的页面
const whiteList = ['/login', '/mobile','/freeLogin','/sharePdf']; // 将 /mobile 加入白名单

// 判断是否为移动端
function isMobile() {
    const ua = navigator.userAgent;
    const mobileKeywords = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|HarmonyOS|HuaweiBrowser/i;
    const isMobileUA = mobileKeywords.test(ua);
    const hasTouch = 'ontouchstart' in window;
    const isSmallScreen = window.innerWidth <= 768;

    return isMobileUA || (hasTouch && isSmallScreen);
}

// 解决页面跳转不在顶部的问题
router.afterEach(() => {
    nextTick(() => {
        const layoutContent = document.getElementById('layout_content');
        if (layoutContent) {
            layoutContent.scrollTop = 0;
        }
    });
});
let userStore = null;

// 路由守卫
router.beforeEach(async (to, from, next) => {
    // 首次访问判断是什么设备，PC端正常加载路由，移动端跳转到指定页面
    let token = localStorage.getItem("token");
    let hrefList = window.location.href;
    let arr = ["/product/", "/scenario/"];
    let homeStore = useHomeStore();
    let shouldControlShop = true;
    arr.forEach((item) => {
        if (hrefList.indexOf(item) !== -1) {
            shouldControlShop = false;
            return;
        }
    });
    homeStore.contralShop = shouldControlShop;
    // 分享链接进去lookpdf页面
    if (!token && !whiteList.includes(to.path)) {
        // 未登录
        if (isMobile()) {
            next('/mobile');
        } else {
            next('/login');
        }
    } else {
        //路由操作
        if (token) {
            operate().then(res => { })
        }
        // 登录或访问白名单页面，直接放行
        next();
    }

})

export default router
