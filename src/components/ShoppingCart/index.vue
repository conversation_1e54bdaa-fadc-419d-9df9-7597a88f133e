<template>
  <div>
    <transition name="slide-fade">
      <div v-if="show" class="box">
        <div class="flex just-sb">
          <div
            v-if="
              (!route.path.includes('proLevel') &&
                !route.path.includes('newProject') &&
                !route.path.includes('scenario') &&
                !route.path.includes('precinctDetail')) ||
              (route.path.includes('newProject') && newProjectType == 2)
            "
            class="combine"
            style="cursor: pointer"
            @click="changeTab(1)"
          >
            <img
              width="14px"
              v-if="selectNum == '1'"
              height="8px"
              src="@/assets/images/home/<USER>"
              style="margin-right: 10px"
            />
            <img
              width="78px"
              v-if="selectNum == '1'"
              height="19px"
              src="@/assets/images/combine/tip.png"
            />
            <img
              width="78px"
              v-else
              height="19px"
              src="@/assets/images/buyList/unselectCombine.png"
            />
          </div>
          <div
            v-if="
              route.path.includes('proLevel') ||
              (route.path.includes('newProject') && newProjectType == 10) ||
              route.path.includes('AISearch') ||
              route.path.includes('scenario') ||
              route.path.includes('precinctDetail')
            "
            class="buyList"
            style="cursor: pointer"
            @click="changeTab(2)"
          >
            <img
              width="14px"
              v-if="selectNum == '2'"
              height="8px"
              src="@/assets/images/home/<USER>"
              style="margin-right: 10px"
            />
            <img
              width="78px"
              v-if="selectNum == '2'"
              height="19px"
              src="@/assets/images/buyList/buyList.png"
            />
            <img
              width="78px"
              v-else
              height="19px"
              src="@/assets/images/buyList/unselectBuy.png"
            />
          </div>
          <div class="flex right">
            <div class="clear" @click="clear">清空</div>
            <div
              class="to-combine margin_l_24"
              v-if="selectNum == '1'"
              @click="toCombine"
            >
              去组合>
            </div>
            <div class="to-combine margin_l_24" v-else @click="toCombine">
              去封装>
            </div>
            <img
              @click="close"
              width="78px"
              class="margin_t_6"
              height="19px"
              src="@/assets/images/combine/close.svg"
            />
          </div>
        </div>
        <div>
          <div class="flex just-sb line">
            <div class="title" v-if="selectNum == '1'">
              方案<span class="sub-count">({{ solutionCount }})</span>
            </div>
            <div class="title" v-else>
              场景<span class="sub-count">({{ solutionCount }})</span>
            </div>
          </div>

          <div
            class="tabContent"
            v-if="solutionList && solutionList.length > 0"
          >
            <div class="cardContent">
              <div class="card_total flex-1">
                <div
                  class="card_content"
                  v-for="(item, index) in solutionList"
                  :key="index"
                >
                  <div style="display: flex; padding: 24px">
                    <a-radio-group :value="valueId" v-if="selectNum == '1'">
                      <a-radio :value="item.id" @change="getId"> </a-radio>
                    </a-radio-group>
                    <a-checkbox-group :value="movalue" v-if="selectNum == '2'">
                      <a-checkbox :value="item.id" @change="getboxId">
                      </a-checkbox>
                    </a-checkbox-group>
                    <div>
                      <div
                        v-if="selectNum == '1'"
                        style="
                          width: 168px;
                          height: 105px;
                          text-align: center;
                          position: relative;
                        "
                        :style="backgroundStyles()"
                      >
                        <p
                          style="
                            font-weight: 700;
                            display: block;
                            color: #1f82c8;
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                            font-size: 10px;
                          "
                        >
                          {{ item.name }}
                        </p>
                      </div>
                      <div v-if="selectNum == '2'">
                        <a-image
                          :width="168"
                          :height="105"
                          :preview="false"
                          v-if="item.logo"
                          :src="`${item.logo}`"
                          style="width: 168px; height: 117px"
                        />
                        <img
                          src="@/assets/images/home/<USER>"
                          style="width: 168px; height: 117px"
                          v-else
                        />
                      </div>
                    </div>
                    <div class="card_center">
                      <div class="card_text">
                        <div class="card_tag">
                          <a-tag color="#D7E6FF">{{ item.industryName }}</a-tag>
                          <div class="card_title">
                            {{ item.name }}
                          </div>
                        </div>
                        <div
                          class="cityStyle"
                          v-if="selectNum == '1'"
                          @click="move(item, '1')"
                        >
                          <img
                            class="add-icon"
                            src=" @/assets/images/AI/cancelAdd.png"
                          /><span class="add"> &nbsp;移出预选</span>
                        </div>
                        <div class="cityStyle" v-else @click="move(item, '1')">
                          <img
                            class="add-icon"
                            src=" @/assets/images/AI/cancelAdd.png"
                          /><span class="add"> &nbsp;移出订购</span>
                        </div>
                      </div>
                      <div class="card_des">
                        {{ item.description }}
                      </div>

                      <div
                        style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                        "
                      >
                        <div style="display: flex" v-if="selectNum == '1'">
                          <!--<a-tag color="#D7E6FF" style="display: block">{{
                            item.label[0]
                          }}</a-tag>
                          <a-tag
                            color="#D7E6FF"
                            v-if="item.label[1]"
                            style="display: block"
                            >{{ item.label[1] }}</a-tag
                          >-->
                        </div>
                        <div style="display: flex">
                          <div class="margin_r_12">
                            <img
                              src="@/assets/images/home/<USER>"
                              style="width: 16px; height: 16px"
                            />
                            <span
                              style="
                                font-size: 12px;
                                color: rgba(0, 0, 0, 0.45);
                              "
                              v-if="item.viewCount"
                              >{{ item.viewCount }}</span
                            >
                            <span v-else>-</span>
                          </div>
                          <div v-if="selectNum == '1'">
                            <img
                              src="@/assets/images/home/<USER>"
                              style="width: 16px; height: 16px"
                            />
                            <span
                              style="
                                font-size: 12px;
                                color: rgba(0, 0, 0, 0.45);
                              "
                              v-if="item.downloadCount"
                              >{{ item.downloadCount }}</span
                            >
                            <span v-else>-</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="emptyPhoto">
            <div>
              <img src="@/assets/images/combine/noData.png" />
            </div>
            <span class="tip" v-if="selectNum == '1'"
              >未选择方案<span
                class="select"
                @click="toList('topContentNew', 'topContentNew')"
                >去选择></span
              ></span
            >
            <span class="tip" v-else
              >未选择场景<span class="select" @click="toList('scenarioPlan')"
                >去选择></span
              ></span
            >
          </div>
        </div>
        <div v-if="selectNum == '1'">
          <div class="flex just-sb line">
            <div class="title">
              方案场景<span class="sub-count">({{ solutionModuleCount }})</span>
            </div>
          </div>
          <div
            class="tabContent"
            v-if="solutionModuleList && solutionModuleList.length > 0"
          >
            <div class="cardContent">
              <div class="card_total flex-1">
                <template
                  v-for="(item, index) in solutionModuleList"
                  :key="index"
                >
                  <div :class="['card_content']">
                    <div style="display: flex; padding: 24px">
                      <a-checkbox-group :value="scevalue">
                        <a-checkbox :value="item.id" @change="getSceneId">
                        </a-checkbox>
                      </a-checkbox-group>
                      <div>
                        <a-image
                          :width="168"
                          :height="1"
                          :preview="false"
                          v-if="item.image"
                          :src="`${item.image}`"
                        />
                        <img
                          src="@/assets/images/home/<USER>"
                          style="width: 168px; height: 117px"
                          v-else
                        />
                      </div>
                      <div class="card_center">
                        <div class="card_text">
                          <div class="card_tag">
                            <a-tag color="#D7E6FF" v-if="item.categoryName">{{
                              item.categoryName
                            }}</a-tag>
                            <div class="card_title">
                              {{ item.name }}
                            </div>
                          </div>
                          <div class="flex">
                            <div
                              class="cityStyle"
                              v-if="selectNum == '1'"
                              @click="move(item, '3')"
                            >
                              <img
                                class="add-icon"
                                src=" @/assets/images/AI/cancelAdd.png"
                              /><span class="add"> &nbsp;移出预选</span>
                            </div>
                            <div
                              class="cityStyle"
                              v-else
                              @click="move(item, '2')"
                            >
                              <img
                                class="add-icon"
                                src=" @/assets/images/AI/cancelAdd.png"
                              /><span class="add"> &nbsp;移出订购</span>
                            </div>
                          </div>
                        </div>
                        <div class="card_des">
                          {{ item.summary }}
                        </div>

                        <div
                          style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          "
                        >
                          <div
                            style="display: flex"
                            v-if="selectNum == '1'"
                          ></div>
                          <div style="display: flex; align-items: center">
                            <img
                              src="@/assets/images/home/<USER>"
                              style="width: 16px; height: 16px"
                            />
                            <span
                              style="
                                font-size: 12px;
                                color: rgba(0, 0, 0, 0.45);
                              "
                              v-if="item.viewCount"
                              >{{ item.viewCount }}</span
                            >
                            <span v-else>-</span>
                            <img
                              src="@/assets/images/home/<USER>"
                              style="
                                width: 16px;
                                height: 16px;
                                margin-left: 18px;
                              "
                            />
                            <span
                              style="
                                font-size: 12px;
                                color: rgba(0, 0, 0, 0.45);
                              "
                              v-if="item.downloadCount"
                              >{{ item.downloadCount }}</span
                            >
                            <span v-else>-</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
          <div v-else class="emptyPhoto">
            <div v-if="selectNum == '1'">
              <img src="@/assets/images/combine/noData.png" />
            </div>
            <span class="tip" v-if="selectNum == '1'"
              >未选择方案场景<span
                class="select"
                @click="toList('topContentNew', 'scene')"
                >去选择></span
              ></span
            >
          </div>
        </div>
        <div>
          <div class="flex just-sb line">
            <div class="title" v-if="selectNum == '1'">
              能力<span class="sub-count">({{ abilityCount }})</span>
            </div>
            <div class="title" v-else>
              产品<span class="sub-count">({{ moduleCount }})</span>
            </div>
          </div>
          <div class="tabContent" v-if="moduleList && moduleList.length > 0">
            <div class="cardContent">
              <div class="card_total flex-1">
                <template v-for="(item, index) in moduleList" :key="index">
                  <div :class="['card_content']">
                    <div style="display: flex; padding: 24px">
                      <a-checkbox-group :value="movalue">
                        <a-checkbox :value="item.id" @change="getModuId">
                        </a-checkbox>
                      </a-checkbox-group>
                      <div>
                        <a-image
                          :width="168"
                          :height="1"
                          :preview="false"
                          v-if="item.abilityPicture"
                          :src="`${item.abilityPicture}`"
                        />
                        <img
                          src="@/assets/images/home/<USER>"
                          style="width: 168px; height: 117px"
                          v-else
                        />
                      </div>
                      <div class="card_center">
                        <div class="card_text">
                          <div class="card_tag">
                            <a-tag color="#D7E6FF" v-if="item.abilityType">{{
                              item.abilityType
                            }}</a-tag>
                            <div class="card_title">
                              {{ item.name }}
                            </div>
                          </div>
                          <div class="flex">
                            <div
                              class="cityStyle"
                              v-if="selectNum == '1'"
                              @click="move(item, '2')"
                            >
                              <img
                                class="add-icon"
                                src=" @/assets/images/AI/cancelAdd.png"
                              /><span class="add"> &nbsp;移出预选</span>
                            </div>
                            <div
                              class="cityStyle"
                              v-else
                              @click="move(item, '2')"
                            >
                              <img
                                class="add-icon"
                                src=" @/assets/images/AI/cancelAdd.png"
                              /><span class="add"> &nbsp;移出订购</span>
                            </div>
                          </div>
                        </div>
                        <div class="card_des">
                          {{ item.abilityIntro }}
                        </div>

                        <div
                          style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          "
                        >
                          <div
                            style="display: flex; align-items: center"
                            v-if="selectNum == '1'"
                          >
                            <a-tag
                              v-if="item.label"
                              color="#D7E6FF"
                              style="display: block"
                              >{{ item.label }}</a-tag
                            >
                          </div>
                          <div style="display: flex; align-items: center">
                            <img
                              src="@/assets/images/home/<USER>"
                              style="width: 16px; height: 16px"
                            />
                            <span
                              style="
                                font-size: 12px;
                                color: rgba(0, 0, 0, 0.45);
                              "
                              v-if="item.viewCount"
                              >{{ item.viewCount }}</span
                            >
                            <span v-else>-</span>
                            <img
                              src="@/assets/images/home/<USER>"
                              v-if="selectNum == '1'"
                              style="
                                width: 16px;
                                height: 16px;
                                margin-left: 18px;
                              "
                            />
                            <span
                              style="
                                font-size: 12px;
                                color: rgba(0, 0, 0, 0.45);
                              "
                              v-if="item.downloadCount && selectNum == '1'"
                              >{{ item.downloadCount }}</span
                            >
                            <span
                              v-else-if="
                                !item.downloadCount && selectNum == '1'
                              "
                              >-</span
                            >
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
          <div v-else class="emptyPhoto margin_b_16">
            <div>
              <img src="@/assets/images/combine/noData.png" />
            </div>
            <span class="tip" v-if="selectNum == '1'"
              >未选择能力<span class="select" @click="toList('moduleList')"
                >去选择></span
              ></span
            >
            <span class="tip" v-else
              >未选择产品<span class="select" @click="toList('productList')"
                >去选择></span
              ></span
            >
          </div>
        </div>
        <div class="margin_b_20" v-if="selectNum == '1'">
          <div class="flex just-sb line">
            <div class="title">
              产品<span class="sub-count">({{ productCount }})</span>
            </div>
          </div>
          <div class="tabContent" v-if="productList && productList.length > 0">
            <div class="cardContent">
              <div class="card_total flex-1">
                <template v-for="(item, index) in productList" :key="index">
                  <div class="card_content">
                    <div style="display: flex; padding: 24px">
                      <a-checkbox-group :value="productvalue">
                        <a-checkbox :value="item.id" @change="getProductId">
                        </a-checkbox>
                      </a-checkbox-group>
                      <div>
                        <a-image
                          :width="168"
                          :height="117"
                          :preview="false"
                          v-if="item.image"
                          :src="`${item.image}`"
                        />
                        <img
                          src="@/assets/images/home/<USER>"
                          style="width: 168px; height: 117px"
                          v-else
                        />
                      </div>
                      <div class="card_center">
                        <div class="card_text">
                          <div class="card_tag">
                            <a-tag color="#D7E6FF" v-if="item.labelName">{{
                              item.labelName
                            }}</a-tag>
                            <div class="card_title">
                              {{ item.name }}
                            </div>
                          </div>
                          <div class="flex">
                            <div
                              class="cityStyle"
                              v-if="selectNum == '1'"
                              @click="move(item, '4')"
                            >
                              <img
                                class="add-icon"
                                src=" @/assets/images/AI/cancelAdd.png"
                              /><span class="add"> &nbsp;移出预选</span>
                            </div>
                            <div
                              class="cityStyle"
                              v-else
                              @click="move(item, '2')"
                            >
                              <img
                                class="add-icon"
                                src=" @/assets/images/AI/cancelAdd.png"
                              /><span class="add"> &nbsp;移出订购</span>
                            </div>
                          </div>
                        </div>
                        <div class="card_des">
                          {{ item.introduction }}
                        </div>
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          "
                        >
                          <div
                            style="display: flex"
                            v-if="selectNum == '1'"
                          ></div>
                          <div style="display: flex; align-items: center">
                            <img
                              src="@/assets/images/home/<USER>"
                              style="width: 16px; height: 16px"
                            />
                            <span
                              style="
                                font-size: 12px;
                                color: rgba(0, 0, 0, 0.45);
                              "
                              v-if="item.viewCount"
                              >{{ item.viewCount }}</span
                            >
                            <span v-else>-</span>
                            <img
                              src="@/assets/images/home/<USER>"
                              style="
                                width: 16px;
                                height: 16px;
                                margin-left: 18px;
                              "
                            />
                            <span
                              style="
                                font-size: 12px;
                                color: rgba(0, 0, 0, 0.45);
                              "
                              v-if="item.downloadCount"
                              >{{ item.downloadCount }}</span
                            >
                            <span v-else>-</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
          <div v-else class="emptyPhoto">
            <div v-if="selectNum == '1'">
              <img src="@/assets/images/combine/noData.png" />
            </div>
            <span class="tip" v-if="selectNum == '1'"
              >未选择产品<span class="select" @click="toList('productList')"
                >去选择></span
              ></span
            >
          </div>
        </div>
      </div>
    </transition>
    <div
      :class="['img', { imgActive: cardShow }, { comBtnShequActive: borderShow && showShequ }, { comBtnActive: borderShow && !showShequ }]"
      @mouseenter="cardEnter"
      @mouseleave="cardLeave"
      @click="showAnimation"
    >
      <img
        width="28px"
        height="28px"
        src="@/assets/images/combine/cartImage.png"
      />
      <p class="prepare" v-html="label"></p>
    </div>
    <a-modal
      v-if="selectNum == '1'"
      v-model:visible="visible"
      title="提示"
      @ok="handleOk"
    >
      <p>已存在定制组合，请确认是否替换已有组合?</p>
    </a-modal>
    <a-modal
      v-if="selectNum == '2'"
      v-model:visible="visible"
      title="提示"
      @ok="handleOk"
    >
      <p>已存在封装产品，请确认是否替换已有封装?</p>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  myShoppingCartList,
  clearShopping,
  toCombinePage,
} from "@/api/combine/shoppingCart.js";
import { useHomeStore } from "@/store";
import { message } from "ant-design-vue";
import bac from "@/assets/images/noDataBac.png";
import { myCombineList } from "@/api/combine/combine.js";
import eventBus from "@/utils/eventBus";
import {
  shopList,
  deleteShop,
  shopTable,
  toShopList,
  go,
} from "@/api/buyList/index";
import { getList } from "@/api/prefecture/home.js";
import { getSceneList } from "@/api/scenario/home";
export default defineComponent({
  props: {
    type: {
      type: String,
      default: "预选组合",
    },
    borderShow: {
      type: Boolean,
      default: false
    },
    showShequ: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const router = useRouter();
    const route = useRoute();
    const counterStore = useHomeStore();
    const data = reactive({
      show: false,
      visible: false,
      title: "XX客户产品方案",
      titleList: [],
      solutionList: [],
      backgroundImage: bac,
      moduleList: [],
      abilityList: [],
      productList: [],
      cardShow: false,
      borderShow: false,
      showShequ: false,
      solutionModuleList: [],
      solutionCount: 0,
      moduleCount: 0,
      abilityCount: 0,
      productCount: 0,
      solutionModuleCount: 0,
      selectList: [],
      valueId: null,
      solveId: {},
      sceneId: {},
      movalue: [],
      scevalue: [],
      productvalue: [],
      selectNum: "1",
      showShop: counterStore.contralShop,
      label:
        route.path.includes("proLevel") ||
        (route.path.includes("newProject") &&
          sessionStorage.getItem("type") == 10) ||
        route.path.includes("scenario") ||
        route.path.includes("precinctDetail")
          ? "订购<br>产品"
          : "预选<br>组合",
      industryId: "",
      bussinessId: "",
      newProjectType: sessionStorage.getItem("type") || "",
    });

    const showAnimation = (val) => {
      data.newProjectType = sessionStorage.getItem("type");
      if (
        route.path.includes("proLevel") ||
        (route.path.includes("newProject") && data.newProjectType == 10) ||
        route.path.includes("scenario") ||
        route.path.includes("precinctDetail")
      ) {
        data.selectNum = "2";
      } else {
        data.selectNum = "1";
      }
      if (!data.show) {
        getAllList();
      }
      data.show = !data.show;
    };
    const getMarketId = () => {
      let params = {
        pageSize: 100,
        pageNo: 1,
      };
      getList(params).then((res) => {
        res.data.records.forEach((item) => {
          if (item.applicationMarket === 1) {
            data.industryId = item.id;
          } else if (item.applicationMarket === 2) {
            data.bussinessId = item.id;
          }
        });
      });
    };
    getMarketId();
    watch(
      () => counterStore.contralShop,
      (nval) => {
        data.showShop = nval;
        // if (data.showShop == true) {
        //   data.label = "预选组合";
        //   data.selectNum = "1";
        // } else {
        //   data.label = "订购产品";
        //   data.selectNum = "2";
        // }
      },
      { deep: true }
    );
    watch(
      () => props.borderShow,
      (nval) => {
        data.borderShow = nval;
      },
    );
    watch(
      () => props.showShequ,
      (nval) => {
        data.showShequ = nval;
      },
    );
    watch(
      () => route.path,
      (newVal, oldVal) => {
        console.log("==========newVal", newVal);
        if (newVal) {
          data.newProjectType = sessionStorage.getItem("type") || "";
          data.solutionList = [];
          data.abilityList = [];
          data.solutionModuleList = [];
          data.moduleList = [];
          data.productList = [];
          if (
            route.path.includes("proLevel") ||
            (route.path.includes("newProject") &&
              sessionStorage.getItem("type") == 10) ||
            route.path.includes("scenario") ||
            route.path.includes("precinctDetail")
          ) {
            data.label = "订购<br>产品";
            data.selectNum = "2";
          } else {
            data.label = "预选<br>组合";
            data.selectNum = "1";
          }
          if (data.show) {
            getAllList();
          }
        }
      },
      { deep: true }
    );
    const getAllList = () => {
      if (data.selectNum == "1") {
        myShoppingCartList().then((res) => {
          data.solutionCount = 0;
          data.moduleCount = 0;
          data.abilityCount = 0;
          data.solutionModuleCount = 0;
          data.productCount = 0;
          data.solutionList = [];
          data.abilityList = [];
          data.solutionModuleList = [];
          data.moduleList = [];
          data.productList = [];
          if (res.data.solutionList) {
            data.solutionList = res.data.solutionList;
            data.solutionList.forEach((item) => {
              //item.label = item.labelName.split(",");
            });
            data.solutionCount = res.data.solutionCount;
          }
          if (res.data.abilityList) {
            data.moduleList = res.data.abilityList;
            data.abilityCount = res.data.abilityCount;
          }
          if (res.data.solutionModuleList) {
            data.solutionModuleList = res.data.solutionModuleList;
            data.solutionModuleCount = res.data.solutionModuleCount;
            data.solutionModuleList.forEach((item) => {
              item.industryName = item.categoryName;
            });
          }
          if (res.data.productList) {
            data.productList = res.data.productList;
            data.productCount = res.data.productCount;
          }
        });
      } else {
        shopList()
          .then((res) => {
            data.solutionCount = 0;
            data.moduleCount = 0;
            data.solutionList = [];
            data.moduleList = [];
            if (res.data.productList) {
              data.moduleList = res.data.productList;
              data.moduleList.forEach((item) => {
                item.abilityIntro = item.introduction;
                item.industryName = item.labelName;
                item.abilityPicture = item.image;
                item.abilityType = item.labelName;
              });
              data.moduleCount = res.data.productList.length;
            }
            if (res.data.sceneList) {
              data.solutionList = res.data.sceneList;
              data.solutionList.forEach((item) => {
                item.logo = item.mainImg;
                item.industryName = item.classifyName;
                item.description = item.introduce;
                item.label = item.labelName;
              });
              data.solutionCount = res.data.sceneList.length;
              data.titleList = res.data.sceneList;
            }
          })
          .catch((error) => {
            console.error("获取购物车列表失败:", error);
          });
      }
    };
    // getAllList();
    eventBus.on("cartRefresh", getAllList);

    // 清空
    const clear = () => {
      let solution = [];
      let modules = [];
      let abiModules = [];
      let products = [];
      data.scevalue = [];
      data.movalue = [];
      data.productvalue = [];
      data.valueId = null;
      if (data.selectNum == "1") {
        if (data.solutionList.length > 0) {
          solution = data.solutionList.map((item) => {
            return { schemeId: item.id, type: "1" };
          });
        }
        if (data.moduleList.length > 0) {
          abiModules = data.moduleList.map((item) => {
            return { schemeId: item.id, type: "2" };
          });
        }
        if (data.solutionModuleList.length > 0) {
          modules = data.solutionModuleList.map((item) => {
            return { schemeId: item.id, type: "3" };
          });
        }
        if (data.productList.length > 0) {
          products = data.productList.map((item) => {
            return { schemeId: item.id, type: "4" };
          });
        }
        if (
          modules.length > 0 ||
          solution.length > 0 ||
          abiModules.length > 0 ||
          products.length > 0
        ) {
          clearShopping([...modules, ...solution, ...abiModules, ...products])
            .then((res) => {
              data.solveId = {};
              data.selectList = [];
              data.solutionCount = 0;
              data.moduleCount = 0;
              data.solutionList = [];
              data.moduleList = [];
              data.solutionModuleList = [];
              data.solutionModuleCount = 0;
              data.abilityCount = 0;
              data.productCount = 0;
              data.productList = [];
              eventBus.emit("moduleAllRefresh"); //能力列表
              eventBus.emit("solutionAllRefresh"); //方案列表+方案场景列表
              eventBus.emit("productRefresh"); //产品列表
              if (route.name == "solveDetailNew")
                eventBus.emit("solutionDetailRefresh"); //方案详情
              if (route.name == "modulelNew")
                eventBus.emit("moduleDetailRefresh"); //能力详情
              if (route.name == "applyNew") eventBus.emit("applyDetailRefresh"); //方案场景详情
            })
            .catch((error) => {});
        }
      } else {
        if (data.solutionList.length > 0) {
          solution = data.solutionList.map((item) => {
            return { productId: item.id, type: "1" };
          });
        }
        if (data.moduleList.length > 0) {
          modules = data.moduleList.map((item) => {
            return { productId: item.id, type: "2" };
          });
        }
        deleteShop([...modules, ...solution])
          .then((res) => {
            data.solveId = {};
            data.selectList = [];
            data.solutionCount = 0;
            data.moduleCount = 0;
            data.solutionList = [];
            data.moduleList = [];
            if (route.name == "productList" || route.name == "proLevel")
              eventBus.emit("productRefresh");
            if (route.name == "productDetail" || route.name == "precinctDetail")
              eventBus.emit("productDetailRefresh");
            if (route.name == "scenarioPlan" || route.name == "proLevel")
              eventBus.emit("scenarioRefresh");
            if (route.name == "scenarioDetail")
              eventBus.emit("scnarioDetailRefresh");
          })
          .catch((error) => {});
      }
    };

    const toCombine = () => {
      if (data.selectNum == "1") {
        console.log(data.solveId, `oooooopppppp`);

        if (Object.keys(data.solveId).length != 0) {
          myCombineList()
            .then((res) => {
              // 提示
              if (
                res.data.list.some((item) => item.list && item.list.length > 0)
              ) {
                data.visible = true;
                data.show = false;
              } else {
                handleOk();
              }
            })
            .catch((error) => {});
        } else {
          message.warning("请选择数据进行组合,方案必选一个");
          return;
        }
      } else if (data.selectNum == "2") {
        if (Object.keys(data.selectList).length > 0) {
          shopTable(1).then((res) => {
            if (res.data.productPackageLists.length > 0) {
              data.visible = true;
              data.show = false;
            } else {
              handleOk();
            }
          });
        } else {
          message.warning("请至少选择一个场景或者产品");
          return;
        }
      }
    };
    const handleOk = () => {
      if (data.selectNum == "1") {
        data.selectList.push(data.solveId);
        clearShopping(data.selectList)
          .then((res) => {
            getAllList();
            eventBus.emit("moduleAllRefresh");
            eventBus.emit("solutionAllRefresh");
          })
          .catch((error) => {});
        console.log("data.selectLis111111111111t", data.selectList);
        toCombinePage({ list: data.selectList, source: "1" }).then((res) => {
          for (let i of data.selectList) {
            if (i.type == 1) {
              localStorage.setItem("AInowCustomizeSolutionId", i.schemeId);
            }
          }
          getAllList();
          data.solutionCount = 0;
          data.moduleCount = 0;
          data.solutionList = [];
          data.moduleList = [];
          data.solutionModuleList = [];
          data.abilityCount = 0;
          data.solveId = {};
          data.selectList = [];
          data.scevalue = [];
          data.valueId = null;
          data.movalue = [];
          data.visible = false;
          eventBus.emit("customRefresh");
          eventBus.emit("cartRefresh");
          router.push({
            query: {
              type: 2,
            },
            name: "newAllProject",
          });
        });
      } else {
        const firstTypeOneItem = data.selectList.find(
          (item) => item.type === 1
        );
        if (firstTypeOneItem) {
          const foundTitle = data.titleList.find(
            (ele) => ele.id === firstTypeOneItem.productId
          );
          if (foundTitle) {
            data.title = foundTitle.name;
          }
        } else {
          data.title = "XX客户产品方案";
        }
        console.log("data.selectLis111111111111t", data.selectList);
        // go toShopList
        toShopList({
          productShoppingCarts: data.selectList,
          source: "1",
          title: data.title,
        }).then((res) => {
          getAllList();
          data.solveId = {};
          data.selectList = [];
          data.valueId = null;
          data.movalue = [];
          data.visible = false;
          counterStore.buyListStroe = {};
          eventBus.emit("buyListRefresh");
          eventBus.emit("cartRefresh");
          router.push({
            query: {
              type: 10,
            },
            name: "newAllProject",
          });
          // router.push({
          //   name: "buyListPage",
          // });
        });
      }
    };

    const toList = (name, type) => {
      if (type === "scene") {
        router.push({
          name: name,
          query: {
            activeNum: "2",
          },
        });
      } else if (type === "topContentNew") {
        router.push({
          name: name,
          query: {
            activeNum: "1",
          },
        });
      } else if (name === "productList" && data.selectNum == "1") {
        if (data.industryId == "") {
          message.warning("暂无行业数据");
        } else {
          router.push({
            name: "proLevel",
            query: {
              appMarket: 1,
              zoneId: data.industryId,
              activeNum: "2",
            },
          });
        }
      } else if (name === "productList" && data.selectNum == "2") {
        if (data.bussinessId == "") {
          message.warning("暂无数据");
        } else {
          router.push({
            name: "proLevel",
            query: {
              appMarket: 2,
              zoneId: data.bussinessId,
              activeNum: "2",
            },
          });
        }
      } else if (name === "scenarioPlan") {
        let pageParams = {
          pageNo: 1,
          pageSize: 100,
          applicationMarkets: 2,
        };
        getSceneList(pageParams).then((res) => {
          if (res.data.totalRows == 0) {
            message.warning("暂无场景数据");
          } else {
            router.push({
              name: "proLevel",
              query: {
                appMarket: 2,
                zoneId: data.bussinessId,
                activeNum: "1",
              },
            });
          }
        });
      } else {
        router.push({
          name: name,
        });
      }
    };

    const move = (item, type) => {
      if (data.selectNum == "1") {
        if (data.solveId && data.solveId.schemeId == item.id) {
          data.solveId = {};
        }
        if (data.productvalue.indexOf(item.id) !== -1) {
          data.productvalue = data.productvalue.filter(
            (ele) => ele !== item.id
          );
        }
        if (data.scevalue.indexOf(item.id) !== -1) {
          data.scevalue = data.scevalue.filter((ele) => ele !== item.id);
        }
        if (data.movalue.indexOf(item.id) !== -1) {
          data.movalue = data.movalue.filter((ele) => ele !== item.id);
        }
        data.selectList = data.selectList.filter(
          (ele) => ele.schemeId != item.id
        );
        clearShopping([{ schemeId: item.id, type: type }])
          .then((res) => {
            console.log(type, `type======================`);
            console.log(route.name, `route.name======================`);
            if (type == "3") {
              eventBus.emit("solutionAllRefresh"); //方案列表+方案场景列表
              if (route.name == "applyNew") eventBus.emit("applyDetailRefresh"); //方案场景详情
            }
            if (type == "1") {
              eventBus.emit("getSchemeList");
              eventBus.emit("solutionAllRefresh"); //方案列表+方案场景列表
              data.valueId = null;
              if (route.name == "solveDetailNew")
                eventBus.emit("solutionDetailRefresh"); //方案详情
            } else {
              //eventBus.emit("productDetailRefresh");
              eventBus.emit("moduleAllRefresh"); //能力列表
              eventBus.emit("solutionAllRefresh"); //方案列表+方案场景列表
              eventBus.emit("productRefresh"); //产品列表
              if (route.name == "solveDetailNew")
                eventBus.emit("solutionDetailRefresh"); //方案详情
              if (route.name == "modulelNew")
                eventBus.emit("moduleDetailRefresh"); //能力详情
              if (route.name == "applyNew") eventBus.emit("applyDetailRefresh"); //方案场景详情
              if (
                route.name == "productDetail" ||
                route.name == "precinctDetail"
              )
                eventBus.emit("productDetailRefresh");
            }
            getAllList();
          })
          .catch((error) => {});
      } else {
        data.selectList = data.selectList.filter(
          (ele) => ele.productId != item.id
        );
        if (data.movalue.indexOf(item.id) !== -1) {
          data.movalue = data.movalue.filter((ele) => ele !== item.id);
        }
        deleteShop([{ productId: item.id, type: type }]).then((res) => {
          console.log(type);
          if (type == "2") {
            eventBus.emit("productRefresh");
            eventBus.emit("productDetailRefresh");
          } else if (type == "1") {
            eventBus.emit("scenarioRefresh");
            eventBus.emit("scnarioDetailRefresh");
          }
          getAllList();
        });
      }
    };

    const close = () => {
      data.show = false;
    };
    const getId = (e) => {
      if (e.target.checked == true) {
        data.solveId = { type: 1, schemeId: e.target.value };
        data.valueId = e.target.value;
      } else {
        data.valueId = "";
        data.solveId = {};
      }
    };
    const getModuId = (e) => {
      if (data.selectNum == "1") {
        data.movalue.push(e.target.value);
        if (e.target.checked == true) {
          data.selectList.push({ type: 2, schemeId: e.target.value });
        } else {
          data.selectList = data.selectList.filter(
            (item) => item.schemeId != e.target.value
          );
          data.movalue = data.movalue.filter((item) => item != e.target.value);
        }
      } else if (data.selectNum == "2") {
        if (e.target.checked == true) {
          data.movalue.push(e.target.value);
          data.selectList.push({ type: 2, productId: e.target.value });
        } else {
          data.selectList = data.selectList.filter(
            (item) => item.productId != e.target.value
          );
          data.movalue = data.movalue.filter((item) => item != e.target.value);
        }
      }
    };
    const getSceneId = (e) => {
      data.scevalue.push(e.target.value);
      console.log("data.selectList", data.selectList);
      if (e.target.checked == true) {
        data.selectList.push({ type: 3, schemeId: e.target.value });
      } else {
        data.selectList = data.selectList.filter(
          (item) => item.schemeId != e.target.value
        );
        data.scevalue = data.scevalue.filter((item) => item != e.target.value);
      }
    };
    const getboxId = (e) => {
      if (e.target.checked == true) {
        data.movalue.push(e.target.value);
        data.selectList.push({ type: 1, productId: e.target.value });
      } else {
        data.selectList = data.selectList.filter(
          (item) => item.productId != e.target.value
        );
        data.movalue = data.movalue.filter((item) => item != e.target.value);
      }
    };
    const getProductId = (e) => {
      data.productvalue.push(e.target.value);
      console.log(" data.productvalue", data.productvalue);
      if (e.target.checked == true) {
        data.selectList.push({ type: 4, schemeId: e.target.value });
      } else {
        data.selectList = data.selectList.filter(
          (item) => item.schemeId != e.target.value
        );
        data.productvalue = data.productvalue.filter(
          (item) => item != e.target.value
        );
      }
      console.log("data.selectList", data.selectList);
    };
    const changeTab = (e) => {
      data.movalue = [];
      data.selectList = [];
      data.selectNum = e;
      getAllList();
    };
    const backgroundStyles = () => {
      return {
        backgroundImage: `url(${data.backgroundImage})`, // 使用模板字符串来插入变量
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
      };
    };
    const cardEnter = () => {
      emit("shopEnter");
      data.cardShow = true;
    };

    const cardLeave = () => {
      emit("shopLeave");
      data.cardShow = false;
    };

    return {
      ...toRefs(data),
      router,
      route,
      backgroundStyles,
      changeTab,
      cardLeave,
      getboxId,
      getId,
      cardEnter,
      getModuId,
      showAnimation,
      toCombine,
      toList,
      clear,
      close,
      move,
      handleOk,
      counterStore,
      getSceneId,
      getProductId,
    };
  },
});
</script>

<style lang="scss" scoped>
.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateY(20px);
  opacity: 0;
}

.img {
  cursor: pointer;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  width: 45px;
  height: 90px;
  background: linear-gradient( 360deg, rgba(232,236,255,0.5) 0%, rgba(255,255,255,0.5) 27%, rgba(255,255,255,0.5) 74%, rgba(232,236,255,0.5) 100%);
  border: 1px solid #ffffff;
  img {
    margin-top: 10px;
  }
}
.comBtnShequActive {
  border-radius: 24px 0px 0px 24px;
}
.comBtnActive {
  border-bottom-left-radius: 24px;
}
.imgActive {
  background: linear-gradient( 360deg, #E8ECFF 0%, #FFFFFF 27%, #FFFFFF 74%, #E8ECFF 100%);
}


:deep(.ant-checkbox-group) {
  display: flex !important;
  justify-content: center !important;
  margin-right: 12px !important;
  align-items: center !important;
}

:deep(.ant-radio-group) {
  display: flex !important;
  justify-content: center !important;
  // margin-right: 12px !important;
  align-items: center !important;
}

.box {
  position: absolute;
  right: -30px;
  bottom: 85px;
  padding: 24px 24px 0 24px;
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  color: rgba(0, 0, 0, 0.85);
  width: 600px;
  max-height: 560px;
  overflow-y: auto;
  z-index: 99999;
  box-shadow: 0px 4px 8px 0px rgba(116, 157, 219, 0.3);
}

.prepare {
  font-weight: 500;
  font-size: 14px;
  color: #0c70eb;
  text-align: center;
  line-height: 1.2;
  margin-top: 4px;
}
</style>

<style lang="scss" scoped src="./components/tableList.scss"></style>
