<template>
  <div>
    <div class="scroll-container">
      <a-checkbox
        v-model:checked="checkedStatus"
        @click="checkedAll"
        :indeterminate="indeterminate"
        >全选</a-checkbox
      >
      <div style="margin-top: 16px">
        <div class="title" style="margin-bottom: 8px">基础方案</div>
        <div v-for="(item, index) in solutionList" :key="index">
          <div
            class="card flex"
            :class="{
              'selected-card': movalue.includes(item.schemeId),
            }"
          >
            <a-checkbox-group :value="movalue">
              <a-checkbox :value="item.schemeId" @change="getboxId">
              </a-checkbox>
            </a-checkbox-group>
            <div class="card_con flex">
              <!-- <img
                :src="`${item.picture}`"
                alt=""
                style="width: 168px; height: 107px"
              /> -->
              <div
                style="
                  width: 168px;
                  height: 105px;
                  margin-right: 18px;
                  text-align: center;
                  position: relative;
                "
                :style="backgroundStyles()"
              >
                <p
                  style="
                    font-weight: 700;
                    display: block;
                    color: #1f82c8;
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    transform: translate(-50%, -50%);
                    font-size: 10px;
                  "
                >
                  {{ item.solutionName }}
                </p>
              </div>
              <div class="content">
                <div class="con_tit">{{ item.solutionName }}</div>
                <div class="flex provider">
                  <p>联系人：{{ item.contact }}</p>
                  <p>联系电话：{{ item.contactPhone }}</p>
                  <p>提供方：{{ item.provider }}</p>
                </div>
                <div class="con_cesc">{{ item.description }}</div>
              </div>
            </div>
            <img class="tips" src="@/assets/images/base.png" alt="" />
          </div>
        </div>
        <div v-if="solutionList.length == 0" class="emptyPhoto">
          <img src="@/assets/images/home/<USER>" />
        </div>
      </div>
      <div>
        <div
          class="flex"
          style="justify-content: space-between; margin-bottom: 8px"
        >
          <span class="title">应用场景</span>
          <div class="warning flex">
            <img src="@/assets/images/action.png" alt="" /><span
              >基础方案下的场景方案不列入调度清单</span
            >
          </div>
        </div>
        <div>
          <div v-for="(item, index) in sortedSceneList" :key="index">
            <div
              class="card flex"
              :class="{
                'selected-card': movalue.includes(item.schemeId),
                'disabled-card': item.classify == 0,
              }"
            >
              <a-checkbox-group :value="movalue">
                <a-checkbox
                  :value="item.schemeId"
                  @change="getboxId"
                  :disabled="item.classify == 0"
                >
                </a-checkbox>
              </a-checkbox-group>
              <div
                class="card_con flex"
                :class="{ 'gray-card': item.classify == 0 }"
              >
                <img
                  :src="`${item.picture}`"
                  alt=""
                  style="width: 168px; height: 107px"
                />
                <div class="content">
                  <div class="con_tit">{{ item.name }}</div>
                  <div class="flex provider">
                    <p>联系人：{{ item.contact }}</p>
                    <p>联系电话：{{ item.contactPhone }}</p>
                    <p>提供方：{{ item.provider }}</p>
                  </div>
                  <div class="con_cesc">{{ item.introduce }}</div>
                </div>
              </div>
              <div class="mask-layer flex" v-if="item.classify == 0">
                <img
                  src="@/assets/images/icon.png"
                  alt="锁定图标"
                  class="lock-icon"
                />
                <div class="mask-text">标准产品不支持调度</div>
              </div>
              <img
                v-if="item.classify == 0"
                class="tips"
                src="@/assets/images/product.png"
                alt=""
              />
              <img
                v-if="item.classify == 2"
                class="tips"
                src="@/assets/images/scene.png"
                alt=""
              />
              <img
                v-if="item.classify == 1"
                class="tips"
                src="@/assets/images/ability.png"
                alt=""
              />
            </div>
          </div>
          <div v-if="sortedSceneList.length == 0" class="emptyPhoto">
            <img src="@/assets/images/home/<USER>" />
          </div>
        </div>
      </div>
    </div>
    <div class="btn_box">
      <span class="refuse" @click="refuse">取消</span>
      <span @click="submit">确定 </span>
    </div>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, ref, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { message } from "ant-design-vue";
import bac from "@/assets/images/noDataBac.png";
export default defineComponent({
  props: {
    combineList: {
      type: Array,
      default: [],
    },
  },
  setup(props, { emit }) {
    const obeySol =
      props.combineList.find((item) => item.type === 1)?.list[0]?.sceneIdList ||
      [];
    const sceneList =
      props.combineList
        .find((item) => item.type === 5)
        ?.list.filter((scene) => !obeySol.includes(scene.schemeId)) || [];

    const data = reactive({
      dataList: props.combineList,
      checkedStatus: false,
      solutionList:
        props.combineList.find((item) => item.type === 1)?.list || [],
      sceneList,
      obeySol,
      movalue: [],
      selectList: [],
      indeterminate: false,
      totalCount: 0,
      backgroundImage: bac,
    });
    data.totalCount =
      data.solutionList.length +
      data.sceneList.filter((item) => item.classify != 0).length;
    console.log(props, `ppppp`);
    const refuse = () => {
      emit("closeModel");
      data.selectList = [];
      data.movalue = [];
    };
    const router = useRouter();
    const getboxId = (e) => {
      const itemId = e.target.value;
      const clickedItem = [...data.solutionList, ...data.sceneList].find(
        (item) => item.schemeId === itemId
      );
      if (clickedItem?.classify === 0) return;
      if (e.target.checked) {
        data.movalue.push(itemId);
        const itemType =
          clickedItem.classify === 5
            ? 1
            : clickedItem.classify === 2
            ? 2
            : clickedItem.classify === 1
            ? 3
            : 1;
        data.selectList.push({ type: itemType, id: itemId });
      } else {
        data.selectList = data.selectList.filter((item) => item.id !== itemId);
        data.movalue = data.movalue.filter((item) => item !== itemId);
      }
      console.log(data.selectList, `data.selectList`);

      updateCheckAllState();
    };
    const checkedAll = () => {
      if (!data.checkedStatus) {
        const allValidItems = [
          ...data.solutionList
            .filter((item) => item.classify !== 0)
            .map((item) => ({
              id: item.schemeId,
              type:
                item.classify === 5
                  ? 1
                  : item.classify === 2
                  ? 2
                  : item.classify === 1
                  ? 3
                  : 1,
            })),
          ...data.sceneList
            .filter((item) => item.classify !== 0)
            .map((item) => ({
              id: item.schemeId,
              type:
                item.classify === 5
                  ? 1
                  : item.classify === 2
                  ? 2
                  : item.classify === 1
                  ? 3
                  : 1,
            })),
        ];
        data.movalue = allValidItems.map((item) => item.id);
        data.selectList = allValidItems;
      } else {
        data.selectList = [];
        data.movalue = [];
      }
      updateCheckAllState();
    };
    const updateCheckAllState = () => {
      const checkedCount = data.movalue.length;
      data.checkedStatus = checkedCount === data.totalCount;
      data.indeterminate = checkedCount > 0 && checkedCount < data.totalCount;
    };
    const submit = () => {
      if (data.selectList.length === 0) {
        message.warning("请至少选择一项方案或场景");
        return; // 阻止继续执行
      }
      let list = data.selectList;
      const grouped = list.reduce((acc, item) => {
        if (!acc[item.type]) {
          acc[item.type] = [];
        }
        acc[item.type].push(item.id);
        return acc;
      }, {});

      const result = Object.entries(grouped).map(([type, ids]) => ({
        type: parseInt(type),
        ids: ids.join(","),
      }));
      console.log(result, `result`);
      const params = {
        idList: JSON.stringify(result),
        action: "edit",
        to: "starWork",
        active: "调度中心",
      };
      const searchParams = new URLSearchParams(params);
      window.location.href =
        window.location.origin +
        "/#/dispatchCenter/transfer?" +
        searchParams.toString();
      data.selectList = [];
      data.movalue = [];
      emit("closeModel");
    };
    const sortedSceneList = computed(() => {
      return [...data.sceneList].sort((a, b) => {
        if (a.classify === 0) return 1;
        if (b.classify === 0) return -1;
        return 0;
      });
    });
    const backgroundStyles = () => {
      return {
        backgroundImage: `url(${data.backgroundImage})`, // 使用模板字符串来插入变量
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
      };
    };
    return {
      ...toRefs(data),
      refuse,
      checkedAll,
      backgroundStyles,
      sortedSceneList,
      getboxId,
      submit,
      router,
    };
  },
});
</script>
<style lang="scss" scoped>
.scroll-container {
  max-height: 650px; /* 可根据需要调整高度 */
  overflow-y: auto;
  padding-right: 8px; /* 防止滚动条遮挡内容 */
  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 0px;
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }
  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
  }
}
.btn_box {
  text-align: center;
  padding-top: 16px;
  span {
    display: inline-block;
    width: 80px;
    height: 40px;
    background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
    border-radius: 4px 4px 4px 4px;
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    line-height: 38px;
    text-align: center;
    cursor: pointer;
  }
  .refuse {
    background: rgba(12, 112, 235, 0.08);
    color: #0c70eb;
    margin-right: 24px;
  }
}
.title {
  font-weight: 500;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
}
.warning {
  font-weight: 400;
  font-size: 14px;
  color: #f51d0f;
  line-height: 22px;
  img {
    width: 22px;
    height: 22px;
  }
}
.mask-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2;
  border-radius: 10px;

  .lock-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 8px;
  }

  .mask-text {
    color: white;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  }
}
::v-deep(.ant-checkbox-disabled) {
  .ant-checkbox-inner {
    background-color: #f5f5f5;
    border-color: #d9d9d9 !important;
  }
}
.card {
  align-items: center;
  background: #f5f7fc;
  border-radius: 10px 10px 10px 10px;
  margin-bottom: 16px;
  padding: 24px;
  border: 1px solid #ffffff;
  position: relative;
  .content {
    flex: 1;
  }
  &.disabled-card {
    position: relative;
    opacity: 0.7;
  }
  .gray-card {
    filter: grayscale(80%);
  }
  .card_con {
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 6, 14, 0.45);
    line-height: 22px;
    margin-left: 18px;
    img {
      margin-right: 18px;
    }
    .provider {
      p {
        margin-right: 32px;
        margin-bottom: 2px;
      }
    }
    .con_tit {
      font-weight: 500;
      font-size: 16px;
      color: #00060e;
      line-height: 22px;
    }
    .con_cesc {
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 6, 14, 0.65);
      line-height: 22px;
      display: -webkit-box; /* 使用旧版弹性盒模型（兼容性） */
      -webkit-box-orient: vertical; /* 垂直排列 */
      -webkit-line-clamp: 2; /* 限制显示的行数 */
      overflow: hidden; /* 隐藏超出部分 */
      text-overflow: ellipsis; /* 超出部分显示省略号 */
      line-height: 1.5; /* 可选：调整行高，防止截断问题 */
      width: 660px;
    }
  }
}
::v-deep(.ant-checkbox-wrapper) {
  span {
    font-weight: 400;
    font-size: 15px;
    color: #00060e;
    line-height: 22px;
  }
}
::v-deep(.ant-modal-header) {
  border-bottom: none !important;
}
.tips {
  width: 76px;
  height: 30px;
  position: absolute; /* 绝对定位 */
  top: -2px; /* 距离卡片顶部距离 */
  right: -4px; /* 距离卡片右侧距离 */
}
.selected-card {
  border: 1px solid #0c70eb;
}
.emptyPhoto {
  text-align: center;
  margin-top: 14px;
  img {
    width: 140px;
    height: 140px;
  }
}
</style>