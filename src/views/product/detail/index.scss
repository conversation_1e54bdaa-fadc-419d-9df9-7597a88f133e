#layout_content {
    background-color: white;
}

.tariff_none {
    background-image: url('@/assets/images/product/tariff.png');
    background-size: 100% 100%;
    height: 260px;
    padding: 70px 108px;
    font-weight: 400;
    font-size: 24px;
    color: rgba(46, 56, 82, 0.65);
    background-color: #fff;
    margin-top: 24px;

    .title {
        font-weight: 700;
        font-size: 20px;
        color: #2e3852;
        line-height: 28px;
        display: block;
        margin-bottom: 10px;
    }

    .con {
        font-size: 18px !important;
    }
}

.banner {
    background-image: url("@/assets/images/solution/detail/ability_banner.png");
    background-size: cover;
    background-repeat: no-repeat;

    width: 100%;
    padding-top: 32px;
    padding-bottom: 32px;
    margin-bottom: 12px;

    .top_card {
        width: 1200px;
        margin: 0px auto;
        height: 100%;
        display: flex;

        .left {
            width: 66%;

            display: inline-block;

            .title {
                font-weight: bold;
                font-size: 24px;
                color: #2E3852;
                line-height: 28px;
                text-align: left;
                display: inline-block;
                margin-right: 8px;
            }

            .bottom1 {
                display: flex;

                p {
                    justify-content: center;
                    align-items: center;
                    display: flex;
                }
            }

            .tips {
                >p {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
            }

            .info_bottom2 {
                font-weight: 300;
                overflow: hidden;
                text-overflow: ellipsis;

                p {
                    min-width: 40px
                }

                img {
                    width: 16px;
                    height: 16px;
                    margin-right: 3px;
                }
            }

            .info_bottom {

                font-weight: 300;
                overflow: hidden;
                text-overflow: ellipsis;
                display: flex;
                //justify-content: space-between;
                justify-content: flex-start;

                p {
                    min-width: 227px;
                    color: #84899A;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    margin-bottom: 4px;
                    margin-right: 0;
                }

                img {
                    width: 16px;
                    height: 16px;
                    margin-right: 3px;
                }
            }

            .code {
                display: inline-block;
                padding: 2px 8px;
                border: none;
                font-size: 16px;
                margin-left: 12px;
                margin-top: -8px;
                color: #0c70eb;
                background: transparent
            }

            .tag {
                position: relative;
                bottom: 3px;
                background-color: #D7E4FB;
                font-weight: 500;
                font-size: 14px;
                color: #2E7FFF;
                line-height: 22px;
                border: none;
            }

            .left_middle {
                p {
                    display: inline-block;
                    font-weight: 400;
                    font-size: 14px;
                    color: #84899A;
                    line-height: 22px;
                    text-align: left;
                    margin-right: 25px;
                }

                .info {
                    font-weight: 400;
                    font-size: 16px;
                    color: #2E3852;
                    line-height: 24px;
                    text-align: left;
                    margin-bottom: 12px;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;

                }


            }

            .left_bottom {
                position: absolute;
                bottom: -14px;
                font-weight: 300;

                div {
                    display: inline-block;
                    // margin-left: 16px;

                    p {
                        display: inline-block;
                        color: #84899A;

                    }

                    img {
                        display: inline-block;
                        margin-bottom: 3px;
                        margin-right: 4px;
                        width: 16px;
                        height: 16px;
                    }
                }

                .btn {
                    background-color: #1A66FB;
                    color: #E2ECFF;
                    width: 144px;
                    height: 40px;
                    border-radius: 4px 4px 4px 4px;
                    font-weight: bold;
                    font-size: 16px;
                    color: #FFFFFF;
                    line-height: 16px;
                }
            }
        }

        .right {
            display: inline-block;
            margin-left: 20px;
            width: 405px;
            height: 236px;

            img {
                width: 405px;
                height: 236px;
            }

        }
    }
}

.active {
    color: red;
}

.cardAll_list {
    background-image: url("@/assets/images/scenario/bac.png");
    background-size: cover;
    background-repeat: no-repeat;
    padding: 40px 80px;
    width: 100%;
    overflow: hidden;

    .desc {
        font-weight: 400;
        font-size: 16px;
        color: #2E3852;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
    }

    .top_key {
        display: flex;
        font-weight: bold;
        font-size: 20px;
        color: #236CFF;
        line-height: 23px;
        margin-bottom: 24px;
        align-items: center;

        img {
            margin-right: 16px;
            width: 40px;
            height: 40px;
        }
    }
}

.addCar {
    margin-top: 16px;

    button {
        background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
        border-radius: 4px 4px 4px 4px;
        padding: 6px 16px;
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 20px;
        border: none;
        cursor: pointer;
    }

    .disabled {
        background: #A6A6A6 !important;
        cursor: not-allowed;

    }
}

.white_bac {
    background: rgba(243, 247, 255, 0.6);
    box-shadow: 0px 2px 4px 0px rgba(77, 120, 170, 0.1);
    border-radius: 26px 26px 26px 26px;
    padding: 5px 16px;
    font-weight: 400;
    font-size: 12px;
    color: #0C213A;
    line-height: 22px;

    span {
        padding-right: 16px;
        border-right: 1px solid #DBE2ED;
        padding-left: 16px;
        border-left: 1px solid #DBE2ED;
    }

}

.introduction {
    font-weight: 400;
    font-size: 16px;
    color: #2E3852;
    line-height: 24px;
    text-align: left;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.content {
    width: 1200px;
    margin: 0 auto;

}

.anchors {
    display: flex;
    // box-shadow: 0px 4px 4px 0px rgba(77, 120, 170, 0.09);
    margin-bottom: 56px;
    justify-content: center;

    :deep(.ant-anchor-ink::before) {
        display: none;
    }

    :deep(.currentActive) {
        a {
            padding-bottom: 8px;
            border-bottom: 2px solid #236CFF;
        }
    }
}


:deep(.ant-anchor-ink) {
    height: 3px !important;
}

:deep(.ant-tabs-tab) {
    font-weight: bold;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 23px;
}

:deep(.ant-tabs-tab-active .ant-tabs-tab) {
    font-size: 20px;
    color: #2E7FFF;
    line-height: 23px;

    .ant-tabs-tab {
        font-weight: bold !important;
    }
}



:deep(.ant-anchor-link) {
    a {
        color: #262626 !important;
    }
}

:deep(.currentActive) {
    a {
        font-weight: bold !important;
        color: #236CFF !important;
    }
}

ul {
    list-style-type: disc;

    li {
        line-height: 20px;
    }
}

.apply {
    margin: 0 80px;
}

.applyCard {
    :deep(.ant-tabs-tab) {
        font-weight: 500;
        font-size: 20px;
        line-height: 23px;
        width: 100%;
    }

    :deep(.ant-tabs-nav-list) {
        display: flex;
        justify-content: space-between;
    }

    :deep(.ant-tabs-nav-container-scrolling) {
        padding: 0;
    }

    :deep(.ant-tabs-tab-prev) {
        display: none;
    }

    :deep(.ant-tabs-tab-next) {
        display: none;
    }

    :deep(.ant-tabs-nav) {
        // width: 100%;
    }

    :deep(.ant-tabs-nav>div:first-child) {
        display: flex;
        justify-content: space-between;
    }

    :deep(.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab) {
        font-weight: bold;
        font-size: 20px;
        color: #2E7FFF;
        line-height: 23px;
    }

    :deep(.ant-tabs-tab-active) {
        font-weight: bold;
        font-size: 20px;
        color: #2E7FFF;
        line-height: 23px;
    }

    :deep(.ant-tabs-content-nav-operations) {
        display: none;
    }

    :deep(.ant-tabs-nav-operations) {
        display: none !important;

    }

    :deep(.ant-tabs-tab) {
        // flex-grow: 1;
        justify-content: center;
        text-align: center;
        margin-top: 19px;
    }
}

.bottom_say {
    padding-top: 40px;
    background-color: #F5F7FC;
    text-align: center;

    .title {
        font-weight: bold;
        font-size: 24px;
        color: #24456A;
        line-height: 28px;
    }

    .area {
        padding: 24px 40px;
        margin: 32px 120px;
        background-color: #FFFFFF;

        .area_tit {
            text-align: left;
            margin-bottom: 16px;

            .area_title {
                font-weight: 500;
                font-size: 14px;
                color: #2E3852;
                line-height: 28px;
            }
        }

        button {
            background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
            border-radius: 2px 2px 2px 2px;
            font-weight: 500;
            font-size: 14px;
            color: #FFFFFF;
            line-height: 20px;
            padding: 6px 16px;
            border: none;
            margin-top: 16px;
            float: right;
        }

        textarea {
            border: none;
            background-color: #F5F7FC;
        }
    }

    .say_list {
        .listBox {
            padding-left: 0;

            .con {
                display: flex;
                margin-bottom: 33px;
                width: 100%;

                .con_left {
                    padding-top: 15px;

                    img {
                        width: 40px;
                        height: 40px;
                    }
                }

                .con_right {
                    margin-left: 16px;
                    text-align: left;
                    font-weight: 400;
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.65);
                    line-height: 24px;

                    .con_title {
                        margin-bottom: 8px;

                        .name {
                            font-weight: 500;
                            color: rgba(0, 0, 0, 0.85);
                            line-height: 22px;
                            margin-right: 12px;
                        }
                    }

                    .con_boot {
                        display: flex;
                        justify-content: space-between;

                        .delete {
                            color: #236CFF;
                        }
                    }
                }
            }
        }
    }

    .showMore {
        width: 100%;
        text-align: center;

        span {
            font-weight: 400;
            font-size: 14px;
            color: #236CFF;
            line-height: 24px;
            cursor: pointer;
        }
    }
}

.tab_content {
    text-align: center;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;

    .tit {
        font-weight: bold;
        font-size: 24px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 28px;
        display: inline-block;
        margin-left: 6px;
        margin-right: 6px;
    }

    img {
        width: 33px;
        height: 22px;
    }

    .left {
        display: inline-block;
        width: 24px;
        height: 24px;
        background-size: contain;
        position: relative;
        top: 3px;
    }

    .right {
        display: inline-block;
        width: 24px;
        height: 24px;
        background-size: contain;
        position: relative;
        top: 3px;
    }
}

.bottom {
    height: 80px;
    width: 100%;
    // background: linear-gradient(90deg, #0142FD 0%, #2475F9 100%);
}

*::-webkit-scrollbar-vertical {
    width: 101px;
}

/* 纵向滚动条轨道样式 */
:deep(::-webkit-scrollbar-track) {
    // background: red !important;
}


.top {
    position: fixed;
    bottom: 11px;
    left: 92%;
    cursor: pointer;
    width: 91px;
    height: 91px;
}

.info_card {
    background: linear-gradient(180deg, #EDF0F9 0%, #FEFEFF 100%);
    box-shadow: 0px 4px 24px 0px #EAEDF3;
    border-radius: 10px 10px 10px 10px;
    border: 2px solid #FFFFFF;
    margin-left: 6.5%;
    margin-right: 6.5%;
    margin-top: 32px;
    margin-bottom: 56px;

    .content {
        background: linear-gradient(196deg, #EAEFF7 0%, rgba(234, 239, 247, 0.41) 100%);
        box-shadow: 0px -8px 32px 0px #FFFFFF, inset 0px 8px 24px 0px #DFE4ED;
        border-radius: 4px 4px 4px 4px;
        opacity: 0.8;
        margin: 32px 40px;
        padding-left: 32px;
        padding-top: 16px;
        padding-bottom: 1px;

        .line {
            width: 100%;
            // margin-bottom: 16px;

            p {
                display: inline-block;
                font-weight: 500;
                font-size: 14px;
                color: #2E3852;
                line-height: 28px;
                text-align: left;
                // margin-left: 25%;
            }

            span {
                font-weight: bold;
            }
        }

        .desc {
            font-weight: 400;
        }
    }
}

:deep(.fixed) {
    position: static !important;
}

:deep(.ant-anchor) {
    padding: 0 24px 0 24px;

    .ant-anchor-link {
        font-weight: 400;
        font-size: 18px;
        color: #24456A;
        padding: 0 16px;

    }
}

.top_nav {
    padding-left: 120px;
    height: 60px;
    background-color: #f5f7fc;
    width: 100%;
    margin-top: 8px;
    padding-right: 70px;
    display: flex;
    justify-content: space-between;
    position: fixed;
    top: 50px;
    z-index: 21;
    padding-top: 20px;

    div {
        display: inline-block;
    }

    .left_nav {
        padding-bottom: 8px;

        .title {
            font-weight: 400;
            font-size: 12px;
            color: #84899A;
            line-height: 20px;
            margin-right: 8px;
            cursor: pointer;
        }

        .current {
            font-weight: 400;
            font-size: 12px;
            color: #2E3852;
            line-height: 20px;
        }
    }

    .right_nav {

        color: #2E7FFF;
        cursor: pointer;
    }
}

.list {
    padding-inline-start: 0;
    list-style-type: none;
    width: 1200px;
    margin: 24px auto;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    li {
        width: 48%;
        display: inline-block;
        background: linear-gradient(180deg, #EDF0F9 0%, #FEFEFF 100%);
        box-shadow: 0px 4px 24px 0px #EAEDF3;
        border-radius: 10px 10px 10px 10px;
        border: 2px solid #FFFFFF;
        padding: 4px 24px 4px 24px;
        margin-bottom: 24px;

        img {
            display: inline-block;
            width: 50px;
            height: 50px;
        }

        p {
            display: inline-block;
            font-weight: 500;
            font-size: 16px;
            color: #2E3852;
            line-height: 28px;
        }

        .left_box {
            display: flex;
            padding-top: 19px;
        }
    }

    .li_box {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    li:nth-of-type(odd) {
        margin-right: 24px;
    }

    .fileText {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }
}


.card {
    box-shadow: none !important;
    padding-bottom: 56px;
    display: flex;

    img {
        height: 280px;
        width: 420px;
        display: inline-block;
    }

    .card_content {
        display: flex;
        width: 100%;

        .card_title {
            font-weight: bold;
            font-size: 18px;
            color: rgba(0, 0, 0, 0.85);
        }

        //   卡片式
        .cards {
            margin-top: 24px;
            display: flex;
            justify-content: start;
            flex-wrap: wrap;

            .item_card {
                width: 374px;
                padding-bottom: 12px;
                background: linear-gradient(163deg, #F1F3F6 0%, #F6F7F9 38%, #FFFFFF 100%);
                box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
                border-radius: 0px 0px 0px 0px;
                border: 2px solid #FFFFFF;
                margin-bottom: 12px;
                margin-right: 39px;

                p {
                    margin-bottom: 6px;
                    padding: 0 12px;
                }

                img {
                    width: 100%;
                    height: 176px;
                }

                .title {
                    margin-top: 12px;
                    font-weight: bold;
                    font-size: 20px;
                    color: #2E3852;
                }

                .desc {
                    font-weight: 400;
                    font-size: 16px !important;
                    color: rgba(46, 56, 82, 0.85);
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 3;
                    /* 控制显示的行数 */
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;


                }
            }

            .item_card:nth-child(3n) {
                margin-right: 0;
            }
        }


        // 左图右描述
        .right {
            background: linear-gradient(163deg, #F2F5F8 0%, #F6F7F9 38%, #FFFFFF 100%);
            box-shadow: -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
            font-weight: 400;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 32px;
            padding: 20px 54px;
            max-height: 280px;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;

            .desc {
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 6;
                /* 控制显示的行数 */
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
            }
        }

        // 文字描述一行两个卡片
        .function {
            margin-top: 24px;
            flex-wrap: wrap;
            justify-content: space-between;

            img {
                margin-right: 12px;
                width: 31px;
                height: 35px;
            }

            .card_list {
                background-image: url("@/assets/images/solution/detail/cardbg.png");
                background-size: cover;
                background-repeat: no-repeat;
                padding: 16px 32px;
                width: 49%;
                margin-bottom: 12px;
                overflow: hidden;

                .desc {
                    font-weight: 400;
                    font-size: 16px;
                    color: #2E3852;
                    overflow: hidden;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                }
            }
        }


        // 文字描述 资费
        .table {
            margin-top: 24px;
            // background: #FBFCFC;
            // box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
            border-radius: 0px 0px 0px 0px;
            // border: 1px solid #FFFFFF;
            padding: 16px 0;

            .tableBox {
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;

                >img {
                    width: calc(33% - 10px);
                    margin-bottom: 20px;
                    border: 2px solid #FFFFFF;
                }

                :deep(.ant-image) {
                    width: calc(33% - 10px);
                }
            }

            .col {
                line-height: 45px;
                text-align: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                padding: 0 24px;
            }

            .td {
                margin: 0 auto;
                margin-bottom: 16px;
                font-weight: 500;
                font-size: 20px;
                text-align: center;
                color: rgba(46, 56, 82, 0.65);
            }

            .tr {

                margin: 0 auto;
                margin-bottom: 12px;
                height: 45px;
                background: #F8FBFF;
                border-radius: 32px 32px 32px 32px;
                font-size: 18px;

                .tariff_index {
                    background: linear-gradient(180deg, #8DB7FF 0%, #65AEFF 100%);
                    box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
                    border-radius: 32px 32px 32px 32px;
                    border: 1px solid #FFFFFF;
                    color: #fff;
                    font-weight: bold;
                    font-size: 18px;
                }

                .name {
                    font-weight: 500;
                    color: #2E3852;
                }

                .price {
                    font-weight: bold;
                    color: #236CFF;
                }


            }

        }

        // 一行一个文字 营销话术
        .market {
            margin-top: 24px;

            img {
                margin-right: 12px;
                width: 31px;
                height: 35px;
            }

            .card_list {
                background-image: url("@/assets/images/solution/detail/cardbg.png");
                background-size: cover;
                background-repeat: no-repeat;
                padding: 16px 32px;
                width: 100%;
                margin-bottom: 12px;
                overflow: hidden;
            }

            .desc {
                font-size: 16px;
            }

        }

        // 订购操作
        .order {
            width: 100%;
            margin-top: 24px;
            background-image: url("@/assets/images/product/step_bg.png");
            background-size: cover;
            background-repeat: no-repeat;

            .card_list {
                position: relative;
                padding: 45px 60px 26px 80px;
                width: 100%;

                border: 1px solid #FFFFFF;

                .left_index {
                    position: relative;
                    left: -61px;
                    top: -38px;
                    font-weight: bold;
                    font-size: 20px;
                    color: #FFFFFF;
                    line-height: 28px;
                    text-shadow: 2px 4px 8px #0E55C5;
                }

                .order_right {
                    width: 100%;
                    font-weight: 400;
                    font-size: 16px;
                    color: rgba(0, 0, 0, 0.65);
                    padding: 0 0 14px 32px;
                    flex: 1;
                }

                .desc {
                    font-weight: 400;
                    font-size: 16px;
                    color: #2E3852;
                    overflow: hidden;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                }

            }
        }
    }
}



:deep(.ant-breadcrumb) {
    li {
        cursor: pointer;

    }
}

.reset_btn {
    background-color: #FFFFFF;
    color: #2E3852;
    margin-right: 24px;
    border: 1px solid #C1CCE5;
    box-shadow: none;
}

.submit_btn {
    background-color: #1A66FB;
    color: #FFFFFF;
}

.btn_box {
    margin-top: 60px;
}

:deep(.ant-tabs-nav-list) {
    width: 100%;
}



:deep(.ant-tabs-tabpane) {
    // background-color: #F5F7FC;
    // margin-top: 32px;
    // height: 220px;
    display: flex;
}

:deep(.ant-tabs-ink-bar) {
    margin-top: 16px;
    height: 3px !important;
}

:deep(.ant-tabs-nav) {
    height: 80px;
    margin: 0;
}

.market_con {
    text-align: center;
    margin-bottom: 40px;

    .title {
        font-weight: bold;
        font-size: 28px;
        color: #24456A;
        line-height: 33px;
        margin-bottom: 56px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .market_tit {
        margin: 0 24px;
    }

    .tit_line {
        display: inline-block;
        width: 48px;
        height: 2px;
        background: linear-gradient(90deg, rgba(36, 69, 106, 0) 0%, #24456A 100%);
    }

    .tit_line_right {
        display: inline-block;
        width: 48px;
        height: 2px;
        background: linear-gradient(90deg, #24456A 0%, rgba(36, 69, 106, 0) 100%);
    }

    .market_box {
        display: flex;
        justify-content: start;
        background: linear-gradient(180deg, #F6F9FF 0%, #FEFEFF 100%);
        box-shadow: 0px 4px 24px 0px #EAEDF3;
        border-radius: 10px 10px 10px 10px;
        border: 2px solid #FFFFFF;
        padding: 24px 24px;

        .con {
            cursor: pointer;
            font-weight: 400;
            font-size: 16px;
            color: #1D3B5F;
            line-height: 32px;
            margin-right: 193px;

            span {
                // margin-top: 16px;

            }
        }

        .con:last-child {
            margin-right: 0;
        }

        img {
            width: 62px;
            height: 48px;
            margin-right: 10px;
        }
    }
}

.apply_info {
    background: #F3F7FF;
    box-shadow: 0px 2px 4px 0px rgba(77, 120, 170, 0.1);
    border-radius: 26px 26px 26px 26px;
    padding: 0 16px;

    font-weight: 400;
    font-size: 12px;
    color: #0C213A;
    line-height: 27px;
    height: 27px;

    span {}
}

.demo_con {
    background: linear-gradient( 163deg, #F2F5F8 0%, #F6F7F9 38%, #FFFFFF 100%);
    box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #FFFFFF;
    padding: 20px 10px;
    margin-bottom: 24px;
    .demo {
        padding: 30px 20px 20px 20px;
        box-shadow: 8px 8px 12px 0px rgba(0,0,0,0.04), -8px -8px 12px 0px rgba(255,255,255,0.5);
        background: linear-gradient( 180deg, #EAEFF7 0%, #F2F3FA 51%, #FFFFFF 100%);
        border-radius: 8px 8px 8px 8px;
        border: 2px solid #FFFFFF;
        position: relative;
        display: flex;
        justify-content: center;
        .demoDiv_line {
        	width: 1px;
        	height: 36px;
        	background-color: #0C70EB33;
        }
        .demoDiv {
        	width: 49%;
        	display: flex;
        	align-items: center;
        	justify-content: center;
        	>img{
        		width: 36px;
        		height: 36px;
        	}
        	.demoDiv_title {
        		font-weight: bold;
        		font-size: 20px;
        		color: #2E3852;
        		text-align: left;
        		>span{
        			color: #0C70EBFF;
        			cursor: pointer;
        		}
        	}
        }
    }
}